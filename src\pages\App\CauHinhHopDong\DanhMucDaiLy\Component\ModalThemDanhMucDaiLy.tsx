import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {Col, Flex, Form, Modal, Row, Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useDanhMucDaiLyContext} from "../index.context";
import {FormChiTietDanhMucDaiLy, IDaiLySelected, IModalThemDanhMucDaiLyRef, IModalTimDaiLyChaRef, ThemDaiLyProps} from "./index.configs";
import {LOAI_DAI_LY, TRANG_THAI_CHI_TIET_DAI_LY} from "../index.configs";
import {ModalTimDaiLyCha} from "./ModalTimDaiLyCha";
import "../index.default.scss";
import TabDaiLyQuanLy from "./TabDaiLyQuanLy";
const {ma, ten, loai, nguoi_dd, dthoai_dd, email_dd, mst_dd, cmt_dd, stt, trang_thai, trang_thai_ten, ma_ct} = FormChiTietDanhMucDaiLy;

const ModalThemDanhMucDaiLyComponent = forwardRef<IModalThemDanhMucDaiLyRef, ThemDaiLyProps>(({listDoiTac, listDaiLy}: ThemDaiLyProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDaiLy?: CommonExecute.Execute.IChiTietDanhMucDaiLy) => {
      setIsOpen(true);
      if (dataDaiLy) setDisableSubmit(true);
      console.log("chitietDaiLy chi tiết", dataDaiLy);
      if (dataDaiLy) setchitietDaiLy(dataDaiLy.dl[0]);
      
    },

    close: () => setIsOpen(false),
  }));

  const [chitietDaiLy, setchitietDaiLy] = useState<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>(null);
  const refModelTimDaiLyCha = useRef<IModalTimDaiLyChaRef>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {loading, onUpdateDanhMucDaiLy, filterParams, setFilterParams, layDanhSachDaiLyPhanTrang, daiLyQuanLySelected} = useDanhMucDaiLyContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const [daiLySelected, setDaiLySelected] = useState<IDaiLySelected | null>(null);
  const [activeTab, setActiveTab] = useState<string>("1");
  // init form data
  useEffect(() => {
    if (chitietDaiLy) {
      const arrFormData = [];
      for (const key in chitietDaiLy) {
        arrFormData.push({
          name: key,
          value: chitietDaiLy[key as keyof CommonExecute.Execute.IChiTietDanhMucDaiLy],
        });
      }
      const daiLyCha = listDaiLy.find(daiLy => daiLy.ma === chitietDaiLy.ma_ct);
      if (daiLyCha) {
        setDaiLySelected({
          ma: daiLyCha.ma,
          ten: daiLyCha.ten,
        });
      } else {
        setDaiLySelected(null);
      }
      form.setFields(arrFormData);
    }
  }, [chitietDaiLy]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setchitietDaiLy(null);
    form.resetFields();
    setFilterParams(filterParams);
    layDanhSachDaiLyPhanTrang(filterParams);
  }, [filterParams]);

  //handel tab change
  const handleTabChange = useCallback((activeKey: string) => {
    setActiveTab(activeKey);
  }, []);
  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDaiLyParams = form.getFieldsValue(); //lấy ra values của form
      console.log("values", values);

      const params = {
        ...values,
        qly: daiLyQuanLySelected.map(row => ({
          ma_chi_nhanh_ql: row.ma_chi_nhanh_ql,
          ma_doi_tac_ql: row.ma_doi_tac_ql,
        })),
        // ma_ct: watchDLCha ? watchDLCha.value : null,
      };
      console.log("paraarram ", params);
      const response = await onUpdateDanhMucDaiLy(params);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
      //cập nhật lại danh mục đại lý
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_CHI_TIET_DAI_LY[0].ma}}>
      {/* Row 1: Đội, Mã đại lý, Tên nguồn lực chính đại diện */}
      <Row gutter={16}>
        {/* {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chitietDaiLy ? true : false})} */}
        {renderFormInputColum({...ma, disabled: chitietDaiLy ? true : false})}
        {renderFormInputColum({...ten}, 16)}
      </Row>

      {/* Row 2: Tên đại lý, Loại đại lý, SĐT nguồn lực chính đại diện */}
      <Row gutter={16}>
        {renderFormInputColum({...loai, options: LOAI_DAI_LY})}
        {renderFormInputColum({...nguoi_dd})}
        {renderFormInputColum({...dthoai_dd})}
      </Row>
      {/* Row 3: Email nguồn lực chính đại diện, Mã số thuế tổ chức, Trạng thái */}
      <Row gutter={16}>
        {renderFormInputColum({...email_dd})}
        {renderFormInputColum({...mst_dd})}
        {renderFormInputColum({...cmt_dd})}
      </Row>

      {/* Row 4: CMND/CCCD cá nhân, Thứ tự hiển thị */}
      <Row gutter={16}>
        {renderFormInputColum({
          ...ma_ct,
          options: [{ma: daiLySelected?.ma, ten: daiLySelected?.ten}],
          open: false,
          dropdownStyle: {display: "none"},
          labelInValue: true,
          onClick: () => refModelTimDaiLyCha.current?.open(),
        })}
        {renderFormInputColum({...stt})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})}
      </Row>
    </Form>
  );
  //render tab
  const renderTabs = () => {
    return (
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane key="1" tab="Thông tin đại lý">
          {renderForm()}
        </Tabs.TabPane>
        <Tabs.TabPane key="2" tab="Cấu hình quản lý đại lý">
          <TabDaiLyQuanLy
            // filterValues={filterValues}
            chiTietDaiLy={chitietDaiLy}
            // onDataChange={handleDonViQuanLyChange} // Truyền callback cho đơn vị quản lý
            // onDataChangeMenu={handleMenuChange}
          />
        </Tabs.TabPane>
      </Tabs>
    );
  };

  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-them-dai-ly"
        maskClosable={false}
        title={
          <HeaderModal
            title={chitietDaiLy ? `Chi tiết đại lý ${chitietDaiLy.dl?.ten}` : "Tạo mới đại lý"}
            trang_thai_ten={chitietDaiLy?.dl?.trang_thai_ten}
            trang_thai={chitietDaiLy?.dl?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "60%",
          sm: "60%",
          md: "60%",
          lg: "60%",
          xl: "60%",
          xxl: "60%",
        }}
        footer={renderFooter}>
        {renderTabs()}
      </Modal>
      <ModalTimDaiLyCha
        ref={refModelTimDaiLyCha}
        onSelectDaiLyCha={daiLyCha => {
          console.log("daiLyCha from ModalTimDaiLyCha", daiLyCha);
          if (daiLyCha) {
            form.setFieldValue("ma_ct", daiLyCha.ma);
            setDaiLySelected(daiLyCha);
          } else {
            form.setFieldValue("ma_ct", null);
          }
        }}
        chiTietDaiLy={chitietDaiLy}
      />
    </Flex>
  );
});

ModalThemDanhMucDaiLyComponent.displayName = "ModalThemDanhMucDaiLyComponent";
export const ModalThemDanhMucDaiLy = memo(ModalThemDanhMucDaiLyComponent, isEqual);
